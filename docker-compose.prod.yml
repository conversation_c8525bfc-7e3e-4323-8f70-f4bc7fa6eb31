version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: sumopod-postgres-prod
    restart: always
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-sumopod}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
      - ./backups:/backups
    networks:
      - sumopod-network-prod
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-sumopod}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Backend Service
  backend:
    image: ${DOCKER_REGISTRY:-}sumopod-backend:${TAG:-latest}
    container_name: sumopod-backend-prod
    restart: always
    environment:
      # Database
      DATABASE_URL: postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-sumopod}
      
      # Authentication
      BETTER_AUTH_SECRET: ${BETTER_AUTH_SECRET}
      BETTER_AUTH_URL: ${BETTER_AUTH_URL}
      BETTER_AUTH_TRUSTED_ORIGINS: ${BETTER_AUTH_TRUSTED_ORIGINS}
      
      # Server
      PORT: 8080
      NODE_ENV: production
      
      # CORS
      CORS_ORIGINS: ${CORS_ORIGINS}
      CORS_ALLOW_HEADERS: ${CORS_ALLOW_HEADERS:-Content-Type,Authorization,X-Session-Token}
      CORS_ALLOW_METHODS: ${CORS_ALLOW_METHODS:-GET,POST,PUT,DELETE,OPTIONS}
      
      # Application
      APP_NAME: ${APP_NAME:-sumopod-backend}
      EXTERNAL_ID_PREFIX: ${EXTERNAL_ID_PREFIX:-sumopod-}
      
      # Xendit
      XENDIT_API_KEY: ${XENDIT_API_KEY}
      XENDIT_API_URL: ${XENDIT_API_URL:-https://api.xendit.co/v2/invoices}
      XENDIT_CALLBACK_TOKEN: ${XENDIT_CALLBACK_TOKEN}
    ports:
      - "${BACKEND_PORT:-8080}:8080"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - sumopod-network-prod
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:8080/api/auth/session', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Frontend Service
  frontend:
    image: ${DOCKER_REGISTRY:-}sumopod-frontend:${TAG:-latest}
    container_name: sumopod-frontend-prod
    restart: always
    ports:
      - "${FRONTEND_PORT:-80}:80"
      - "${FRONTEND_SSL_PORT:-443}:443"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - sumopod-network-prod
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Reverse Proxy (optional - for SSL termination and load balancing)
  nginx-proxy:
    image: nginx:1.25-alpine
    container_name: sumopod-nginx-proxy
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx-proxy.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    networks:
      - sumopod-network-prod
    profiles:
      - proxy
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  sumopod-network-prod:
    driver: bridge
    name: sumopod-network-prod

volumes:
  postgres_data_prod:
    name: sumopod-postgres-data-prod
