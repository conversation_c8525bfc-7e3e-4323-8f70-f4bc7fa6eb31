# syntax = docker/dockerfile:1

# Build arguments
ARG NODE_VERSION=20.18.0

# Base stage with common dependencies
FROM node:${NODE_VERSION}-slim as base

# Set working directory
WORKDIR /app

# Install system dependencies and security updates
RUN apt-get update -qq && \
    apt-get install -y --no-install-recommends \
    openssl \
    libssl-dev \
    ca-certificates \
    && apt-get upgrade -y \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user for security
RUN groupadd --gid 1001 --system nodejs && \
    useradd --uid 1001 --system --gid nodejs --create-home --shell /bin/bash nodejs

# Set environment variables
ENV NODE_ENV=production
ENV NPM_CONFIG_UPDATE_NOTIFIER=false
ENV NPM_CONFIG_FUND=false

# Dependencies stage
FROM base as deps

# Copy package files
COPY --chown=nodejs:nodejs package*.json ./

# Install all dependencies (including dev for build)
RUN npm ci --include=dev --frozen-lockfile && \
    npm cache clean --force

# Build stage
FROM base as build

# Install build dependencies
RUN apt-get update -qq && \
    apt-get install -y --no-install-recommends \
    python3 \
    make \
    g++ \
    pkg-config \
    build-essential \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Copy dependencies from deps stage
COPY --from=deps --chown=nodejs:nodejs /app/node_modules ./node_modules

# Copy source code
COPY --chown=nodejs:nodejs package*.json ./
COPY --chown=nodejs:nodejs tsconfig.json ./
COPY --chown=nodejs:nodejs prisma ./prisma
COPY --chown=nodejs:nodejs src ./src

# Generate Prisma Client
RUN npx prisma generate

# Build the application
RUN npm run build

# Remove development dependencies
RUN npm ci --omit=dev --frozen-lockfile && \
    npm cache clean --force

# Production stage
FROM base as production

# Copy built application and production dependencies
COPY --from=build --chown=nodejs:nodejs /app/dist ./dist
COPY --from=build --chown=nodejs:nodejs /app/node_modules ./node_modules
COPY --from=build --chown=nodejs:nodejs /app/package*.json ./
COPY --from=build --chown=nodejs:nodejs /app/prisma ./prisma

# Copy docker entrypoint script
COPY --chown=nodejs:nodejs docker-entrypoint ./docker-entrypoint
RUN chmod +x ./docker-entrypoint

# Switch to non-root user
USER nodejs

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:' + (process.env.PORT || 8080) + '/api/auth/session', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"

# Expose port
EXPOSE 8080

# Set entrypoint and default command
ENTRYPOINT ["./docker-entrypoint"]
CMD ["npm", "run", "start"]
