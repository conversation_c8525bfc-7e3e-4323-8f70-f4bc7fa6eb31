# syntax = docker/dockerfile:1

# Production-ready backend Dockerfile for Sumopod
ARG NODE_VERSION=20.18.0

# Base stage with common dependencies
FROM node:${NODE_VERSION}-slim as base

WORKDIR /app

# Install system dependencies
RUN apt-get update -qq && \
    apt-get install -y --no-install-recommends \
    openssl \
    libssl-dev \
    ca-certificates \
    && apt-get upgrade -y \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user
RUN groupadd --gid 1001 --system nodejs && \
    useradd --uid 1001 --system --gid nodejs --create-home --shell /bin/bash nodejs

# Set production environment
ENV NODE_ENV=production
ENV NPM_CONFIG_UPDATE_NOTIFIER=false
ENV NPM_CONFIG_FUND=false

# Dependencies stage
FROM base as deps

COPY --chown=nodejs:nodejs package*.json ./
RUN npm ci --include=dev --frozen-lockfile && \
    npm cache clean --force

# Build stage
FROM base as build

# Install build dependencies
RUN apt-get update -qq && \
    apt-get install -y --no-install-recommends \
    python3 \
    make \
    g++ \
    pkg-config \
    build-essential \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Copy dependencies and source code
COPY --from=deps --chown=nodejs:nodejs /app/node_modules ./node_modules
COPY --chown=nodejs:nodejs package*.json ./
COPY --chown=nodejs:nodejs tsconfig.json ./
COPY --chown=nodejs:nodejs prisma ./prisma
COPY --chown=nodejs:nodejs src ./src

# Generate Prisma Client and build
RUN npx prisma generate && \
    npm run build && \
    npm ci --omit=dev --frozen-lockfile && \
    npm cache clean --force

# Production stage
FROM base as production

# Copy built application
COPY --from=build --chown=nodejs:nodejs /app/dist ./dist
COPY --from=build --chown=nodejs:nodejs /app/node_modules ./node_modules
COPY --from=build --chown=nodejs:nodejs /app/package*.json ./
COPY --from=build --chown=nodejs:nodejs /app/prisma ./prisma
COPY --chown=nodejs:nodejs docker-entrypoint ./docker-entrypoint

RUN chmod +x ./docker-entrypoint

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE 8080

# Start application
ENTRYPOINT ["./docker-entrypoint"]
CMD ["npm", "run", "start"]
