version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: sumopod-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: sumopod
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - sumopod-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d sumopod"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend Service
  backend:
    build:
      context: ./hono-backend
      dockerfile: Dockerfile
      target: production
    container_name: sumopod-backend
    restart: unless-stopped
    environment:
      # Database
      DATABASE_URL: ********************************************/sumopod
      
      # Authentication
      BETTER_AUTH_SECRET: your-super-secret-key-change-this-in-production
      BETTER_AUTH_URL: http://localhost:8080
      BETTER_AUTH_TRUSTED_ORIGINS: http://localhost:3000,http://localhost:3001
      
      # Server
      PORT: 8080
      NODE_ENV: development
      
      # CORS
      CORS_ORIGINS: http://localhost:3000,http://localhost:3001
      CORS_ALLOW_HEADERS: Content-Type,Authorization,X-Session-Token
      CORS_ALLOW_METHODS: GET,POST,PUT,DELETE,OPTIONS
      
      # Application
      APP_NAME: sumopod-backend
      EXTERNAL_ID_PREFIX: sumopod-
      
      # Xendit (add your actual keys)
      XENDIT_API_KEY: your-xendit-api-key
      XENDIT_API_URL: https://api.xendit.co/v2/invoices
      XENDIT_CALLBACK_TOKEN: your-xendit-callback-token
    ports:
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - sumopod-network
    volumes:
      - ./hono-backend/src:/app/src:ro
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:8080/api/auth/session', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend Service
  frontend:
    build:
      context: ./sumopod
      dockerfile: Dockerfile
      target: production
      args:
        VITE_API_BASE_URL: http://localhost:8080
        VITE_GOLD_API_URL: https://logam-mulia-api.vercel.app/prices/anekalogam
    container_name: sumopod-frontend
    restart: unless-stopped
    ports:
      - "3000:80"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - sumopod-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Development Frontend (optional - for hot reload)
  frontend-dev:
    build:
      context: ./sumopod
      dockerfile: Dockerfile
      target: build
    container_name: sumopod-frontend-dev
    restart: unless-stopped
    environment:
      VITE_API_BASE_URL: http://localhost:8080
      VITE_GOLD_API_URL: https://logam-mulia-api.vercel.app/prices/anekalogam
    ports:
      - "3001:3001"
    volumes:
      - ./sumopod/src:/app/src:ro
      - ./sumopod/public:/app/public:ro
    command: npm run dev -- --host 0.0.0.0 --port 3001
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - sumopod-network
    profiles:
      - dev

networks:
  sumopod-network:
    driver: bridge
    name: sumopod-network

volumes:
  postgres_data:
    name: sumopod-postgres-data
