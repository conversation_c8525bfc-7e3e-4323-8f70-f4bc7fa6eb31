# =============================================================================
# PRODUCTION ENVIRONMENT CONFIGURATION FOR SUMOPOD
# =============================================================================
# IMPORTANT: Copy this file to .env.production and fill in all values
# Never commit production secrets to version control!

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
POSTGRES_DB=sumopod
POSTGRES_USER=postgres
# CRITICAL: Use a strong, unique password for production
POSTGRES_PASSWORD=CHANGE_THIS_TO_A_SECURE_PASSWORD

# Full database URL (automatically constructed)
DATABASE_URL=********************************************************************/sumopod

# =============================================================================
# BACKEND AUTHENTICATION CONFIGURATION
# =============================================================================
# CRITICAL: Generate a secure random string (at least 64 characters)
BETTER_AUTH_SECRET=CHANGE_THIS_TO_A_SECURE_RANDOM_STRING_AT_LEAST_64_CHARS

# CRITICAL: Update with your actual production URLs
BETTER_AUTH_URL=https://your-backend-domain.com
BETTER_AUTH_TRUSTED_ORIGINS=https://your-frontend-domain.com

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# CRITICAL: Update with your actual production domains
CORS_ORIGINS=https://your-frontend-domain.com
CORS_ALLOW_HEADERS=Content-Type,Authorization,X-Session-Token
CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
APP_NAME=sumopod-backend
EXTERNAL_ID_PREFIX=sumopod-

# =============================================================================
# XENDIT PAYMENT CONFIGURATION (PRODUCTION)
# =============================================================================
# CRITICAL: Use your production Xendit credentials
XENDIT_API_KEY=CHANGE_THIS_TO_YOUR_PRODUCTION_XENDIT_API_KEY
XENDIT_API_URL=https://api.xendit.co/v2/invoices
XENDIT_CALLBACK_TOKEN=CHANGE_THIS_TO_YOUR_PRODUCTION_XENDIT_CALLBACK_TOKEN

# =============================================================================
# FRONTEND ENVIRONMENT VARIABLES
# =============================================================================
# CRITICAL: Update with your actual production API URL
VITE_API_BASE_URL=https://your-backend-domain.com
VITE_GOLD_API_URL=https://logam-mulia-api.vercel.app/prices/anekalogam

# =============================================================================
# SECURITY NOTES
# =============================================================================
# 1. Never commit this file to version control
# 2. Use environment-specific secrets management in production
# 3. Regularly rotate passwords and API keys
# 4. Use strong, unique passwords for all services
# 5. Enable SSL/TLS for all production endpoints
# 6. Consider using Docker secrets for sensitive data
