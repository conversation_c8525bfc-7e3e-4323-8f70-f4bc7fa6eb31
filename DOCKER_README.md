# 🐳 Sumopod Production Docker Setup

This guide provides instructions for the production-ready Docker containerization of the Sumopod application, optimized for cloud deployment.

## 📋 Table of Contents

- [Architecture Overview](#architecture-overview)
- [Prerequisites](#prerequisites)
- [Quick Start](#quick-start)
- [Production Deployment](#production-deployment)
- [Claw Cloud Deployment](#claw-cloud-deployment)
- [Monitoring](#monitoring)
- [Troubleshooting](#troubleshooting)

## 🏗️ Architecture Overview

The Sumopod application uses a production-ready microservices architecture:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Database      │
│   (Nginx)       │    │   (Hono.js)     │    │  (PostgreSQL)   │
│   Port: 80      │◄──►│   Port: 8080    │◄──►│   Port: 5432    │
│   128MB RAM     │    │   512MB RAM     │    │   512MB RAM     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Production Services:
- **Frontend**: Optimized React build served by Nginx (128MB RAM)
- **Backend**: Hono.js API with authentication and payments (512MB RAM)
- **Database**: PostgreSQL with persistent storage (512MB RAM)
- **Security**: Non-root users, minimal attack surface
- **Performance**: Multi-stage builds, optimized layers

## 🔧 Prerequisites

Before starting, ensure you have:

- **Docker Desktop** (version 20.10+) installed and running
- **Docker Hub account** for image registry
- **Claw Cloud account** for deployment

### Verify Installation

```bash
docker --version
docker login
```

## 🚀 Quick Start

### 1. Configure Production Environment

```bash
# Copy production template
cp .env.production.template .env.production

# Edit with your production values
nano .env.production
```

### 2. Build Production Images

```bash
# Build and push to Docker Hub
./scripts/build-production.sh --registry=yourusername/ --push
```

### 3. Deploy to Claw Cloud

```bash
# Prepare for Claw Cloud deployment
./scripts/deploy-to-claw-cloud.sh --username=yourusername
```

### 4. Monitor in Docker Desktop

- Open Docker Desktop
- Check **Images** tab for built images
- Monitor **Containers** tab for running services

## 🚀 Production Deployment

### 1. Environment Configuration

Create and configure your production environment:

```bash
# Copy template
cp .env.production.template .env.production

# Required production variables
POSTGRES_PASSWORD=your-secure-password
BETTER_AUTH_SECRET=your-64-char-secret
BETTER_AUTH_URL=https://api.your-domain.com
BETTER_AUTH_TRUSTED_ORIGINS=https://your-domain.com
XENDIT_API_KEY=your-production-key
XENDIT_CALLBACK_TOKEN=your-callback-token
VITE_API_BASE_URL=https://api.your-domain.com
```

### 2. Build Production Images

```bash
# Build optimized production images
./scripts/build-production.sh --registry=yourusername/ --push
```

### 3. Local Production Testing

```bash
# Test production setup locally
./scripts/deploy-production.sh --build

# Check status
./scripts/deploy-production.sh --action=status

# View logs
./scripts/deploy-production.sh --action=logs

# Stop services
./scripts/deploy-production.sh --action=down
```

## ☁️ Claw Cloud Deployment

### 1. Prepare for Cloud Deployment

```bash
# Build, push, and generate deployment config
./scripts/deploy-to-claw-cloud.sh --username=yourusername
```

### 2. Deploy on Claw Cloud

1. **Login**: Visit https://run.claw.cloud/
2. **Create Project**: Name it `sumopod-production`
3. **Deploy Services**:
   - Upload generated `claw-cloud-deployment.yml`
   - Or manually create services using the provided configuration

### 3. Service Configuration

**Database Service**:
- Image: `postgres:15-alpine`
- Memory: 512MB
- Storage: 10GB

**Backend Service**:
- Image: `yourusername/sumopod-backend:latest`
- Memory: 512MB
- Port: 8080

**Frontend Service**:
- Image: `yourusername/sumopod-frontend:latest`
- Memory: 128MB
- Port: 80

## ☁️ Cloud Deployment

### Google Cloud Run

1. **Setup Google Cloud**:
```bash
# Install Google Cloud SDK
# https://cloud.google.com/sdk/docs/install

# Login and set project
gcloud auth login
gcloud config set project your-project-id

# Enable required APIs
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com
```

2. **Build and push images**:
```bash
# Configure Docker for GCR
gcloud auth configure-docker

# Tag for Google Container Registry
docker tag sumopod-backend:latest gcr.io/your-project-id/sumopod-backend
docker tag sumopod-frontend:latest gcr.io/your-project-id/sumopod-frontend

# Push to GCR
docker push gcr.io/your-project-id/sumopod-backend
docker push gcr.io/your-project-id/sumopod-frontend
```

3. **Deploy services**:
```bash
# Deploy backend with environment variables
gcloud run deploy sumopod-backend \
  --image gcr.io/your-project-id/sumopod-backend \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars="DATABASE_URL=your-db-url,BETTER_AUTH_SECRET=your-secret"

# Deploy frontend
gcloud run deploy sumopod-frontend \
  --image gcr.io/your-project-id/sumopod-frontend \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

### Claw Cloud (run.claw.cloud)

1. **Prepare for Claw Cloud**:
```bash
# Build production images
./scripts/build.sh --env=prod --tag=latest

# Push to Docker Hub (Claw Cloud supports Docker Hub)
./scripts/push-to-dockerhub.sh --username=yourusername
```

2. **Deploy to Claw Cloud**:
   - Visit https://run.claw.cloud/
   - Create a new project
   - Add your Docker Hub images:
     - Backend: `yourusername/sumopod-backend:latest`
     - Frontend: `yourusername/sumopod-frontend:latest`
   - Configure environment variables from your `.env.production`
   - Set up database (PostgreSQL)
   - Deploy services

### AWS ECS

1. **Push to Amazon ECR**:
```bash
# Create ECR repositories
aws ecr create-repository --repository-name sumopod-backend
aws ecr create-repository --repository-name sumopod-frontend

# Get login token
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin your-account.dkr.ecr.us-east-1.amazonaws.com

# Tag and push
docker tag sumopod-backend:latest your-account.dkr.ecr.us-east-1.amazonaws.com/sumopod-backend:latest
docker push your-account.dkr.ecr.us-east-1.amazonaws.com/sumopod-backend:latest
```

2. **Create ECS task definition and service**:
   - Use AWS Console or CLI to create ECS cluster
   - Define task definitions with your ECR images
   - Configure environment variables and networking
   - Create services and load balancers

### DigitalOcean App Platform

1. **Connect repository**:
   - Go to DigitalOcean App Platform
   - Connect your GitHub repository
   - Configure build settings to use Dockerfiles

2. **Configure services**:
   - Backend: Use `hono-backend/Dockerfile`
   - Frontend: Use `sumopod/Dockerfile`
   - Database: Add managed PostgreSQL database
   - Set environment variables

## 🔍 Troubleshooting

### Common Issues

#### 1. Database Connection Failed
```bash
# Check if PostgreSQL is running
docker-compose ps postgres

# Check database logs
docker-compose logs postgres

# Verify connection string
echo $DATABASE_URL
```

#### 2. Frontend Can't Connect to Backend
```bash
# Check backend health
curl http://localhost:8080/api/auth/session

# Verify CORS settings
docker-compose logs backend | grep CORS

# Check environment variables
docker-compose exec frontend env | grep VITE_API_BASE_URL
```

#### 3. Build Failures
```bash
# Clear Docker cache
docker system prune -a

# Rebuild without cache
./scripts/build.sh --no-cache

# Check Dockerfile syntax
docker build --dry-run -f hono-backend/Dockerfile hono-backend/
```

#### 4. Permission Issues
```bash
# Fix script permissions
chmod +x scripts/*.sh

# Check Docker permissions
docker run hello-world
```

### Debugging Commands

```bash
# Enter container shell
docker-compose exec backend bash
docker-compose exec frontend sh

# Check container resources
docker stats

# View detailed logs
docker-compose logs --tail=100 -f backend

# Inspect container
docker inspect sumopod-backend
```

## 📚 Best Practices

### Security
- ✅ Use non-root users in containers
- ✅ Implement health checks
- ✅ Use multi-stage builds
- ✅ Scan images for vulnerabilities
- ✅ Use secrets management for production

### Performance
- ✅ Optimize Docker layer caching
- ✅ Use .dockerignore files
- ✅ Minimize image sizes
- ✅ Use Alpine Linux base images
- ✅ Enable gzip compression

### Monitoring
- ✅ Implement comprehensive logging
- ✅ Set up health checks
- ✅ Monitor resource usage
- ✅ Use structured logging
- ✅ Set up alerts for failures

### Development
- ✅ Use Docker Compose for local development
- ✅ Implement hot reload for development
- ✅ Use environment-specific configurations
- ✅ Version your Docker images
- ✅ Document all environment variables

## 📞 Support

If you encounter issues:

1. Check the [Troubleshooting](#troubleshooting) section
2. Review Docker and application logs
3. Verify environment configuration
4. Check network connectivity between services
5. Ensure all required ports are available

For additional help, please refer to the main project documentation or create an issue in the repository.
