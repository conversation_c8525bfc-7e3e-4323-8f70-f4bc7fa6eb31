# 🐳 Sumopod Docker Containerization Guide

This guide provides comprehensive instructions for containerizing, building, and deploying the Sumopod application using Docker.

## 📋 Table of Contents

- [Architecture Overview](#architecture-overview)
- [Prerequisites](#prerequisites)
- [Quick Start](#quick-start)
- [Environment Configuration](#environment-configuration)
- [Building Images](#building-images)
- [Local Development](#local-development)
- [Production Deployment](#production-deployment)
- [Docker Hub Deployment](#docker-hub-deployment)
- [Cloud Deployment](#cloud-deployment)
- [Troubleshooting](#troubleshooting)
- [Best Practices](#best-practices)

## 🏗️ Architecture Overview

The Sumopod application is containerized using a microservices architecture:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Database      │
│   (React/Vite)  │    │   (Hono.js)     │    │  (PostgreSQL)   │
│   Port: 3000    │◄──►│   Port: 8080    │◄──►│   Port: 5432    │
│   Nginx         │    │   Node.js       │    │   Persistent    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Services:
- **Frontend**: React application served by Nginx
- **Backend**: Hono.js API server with authentication and payment processing
- **Database**: PostgreSQL with Prisma ORM
- **Networking**: Docker bridge network for service communication
- **Volumes**: Persistent storage for database data

## 🔧 Prerequisites

Before starting, ensure you have:

- **Docker Desktop** (version 20.10+)
- **Docker Compose** (version 2.0+)
- **Git** for cloning the repository
- **Node.js** (optional, for local development)

### Verify Installation

```bash
docker --version
docker-compose --version
```

## 🚀 Quick Start

### 1. Clone and Setup

```bash
git clone <your-repo-url>
cd sumopod-project
```

### 2. Configure Environment

```bash
# Copy environment template
cp .env.development .env

# Edit the .env file with your configuration
nano .env
```

### 3. Start Development Environment

```bash
# Start all services
./scripts/deploy.sh --env=dev

# Or using Docker Compose directly
docker-compose up -d
```

### 4. Access the Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8080
- **Development Frontend**: http://localhost:3001 (with hot reload)

## ⚙️ Environment Configuration

### Environment Files

- `.env.development` - Development settings
- `.env.production` - Production settings
- `.env.example` - Template with all variables

### Required Variables

```bash
# Database
DATABASE_URL=********************************************/sumopod

# Authentication
BETTER_AUTH_SECRET=your-secret-key
BETTER_AUTH_URL=http://localhost:8080
BETTER_AUTH_TRUSTED_ORIGINS=http://localhost:3000

# Xendit Payment
XENDIT_API_KEY=your-api-key
XENDIT_CALLBACK_TOKEN=your-callback-token

# Frontend
VITE_API_BASE_URL=http://localhost:8080
```

## 🔨 Building Images

### Using Build Script

```bash
# Build development images
./scripts/build.sh --env=dev

# Build production images
./scripts/build.sh --env=prod --tag=v1.0.0

# Build without cache
./scripts/build.sh --no-cache
```

### Manual Build

```bash
# Backend
docker build -t sumopod-backend:latest -f hono-backend/Dockerfile hono-backend/

# Frontend
docker build -t sumopod-frontend:latest \
  --build-arg VITE_API_BASE_URL=http://localhost:8080 \
  -f sumopod/Dockerfile sumopod/
```

## 💻 Local Development

### Start Development Environment

```bash
# Start all services
./scripts/deploy.sh --env=dev

# Start with build
./scripts/deploy.sh --env=dev --build

# View logs
./scripts/deploy.sh --action=logs
```

### Development with Hot Reload

```bash
# Start with development profile
docker-compose --profile dev up -d

# This starts:
# - Production frontend (port 3000)
# - Development frontend with hot reload (port 3001)
# - Backend (port 8080)
# - Database (port 5432)
```

### Useful Commands

```bash
# Check service status
./scripts/deploy.sh --action=status

# Restart services
./scripts/deploy.sh --action=restart

# Stop all services
./scripts/deploy.sh --action=down

# View specific service logs
docker-compose logs -f backend
```

## 🚀 Production Deployment

### 1. Configure Production Environment

```bash
# Copy production template
cp .env.production .env

# Update with production values
nano .env
```

**Critical Production Settings:**
- Strong database password
- Secure authentication secret (64+ characters)
- Production domain URLs
- Production Xendit credentials

### 2. Deploy to Production

```bash
# Build and deploy production
./scripts/deploy.sh --env=prod --build

# Or using production compose file
docker-compose -f docker-compose.prod.yml up -d
```

### 3. Production Health Checks

```bash
# Check all services
./scripts/deploy.sh --env=prod --action=status

# Manual health checks
curl http://localhost:8080/api/auth/session
curl http://localhost:80/health
```

## 🐙 Docker Hub Deployment

### 1. Push to Docker Hub

```bash
# Login and push
./scripts/push-to-dockerhub.sh --username=yourusername --login

# Push with custom tag
./scripts/push-to-dockerhub.sh --username=yourusername --tag=v1.0.0
```

### 2. Deploy from Docker Hub

```bash
# Update .env.production
DOCKER_REGISTRY=yourusername/
TAG=latest

# Deploy
./scripts/deploy.sh --env=prod --pull
```

## ☁️ Cloud Deployment

### Google Cloud Run

1. **Setup Google Cloud**:
```bash
# Install Google Cloud SDK
# https://cloud.google.com/sdk/docs/install

# Login and set project
gcloud auth login
gcloud config set project your-project-id

# Enable required APIs
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com
```

2. **Build and push images**:
```bash
# Configure Docker for GCR
gcloud auth configure-docker

# Tag for Google Container Registry
docker tag sumopod-backend:latest gcr.io/your-project-id/sumopod-backend
docker tag sumopod-frontend:latest gcr.io/your-project-id/sumopod-frontend

# Push to GCR
docker push gcr.io/your-project-id/sumopod-backend
docker push gcr.io/your-project-id/sumopod-frontend
```

3. **Deploy services**:
```bash
# Deploy backend with environment variables
gcloud run deploy sumopod-backend \
  --image gcr.io/your-project-id/sumopod-backend \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars="DATABASE_URL=your-db-url,BETTER_AUTH_SECRET=your-secret"

# Deploy frontend
gcloud run deploy sumopod-frontend \
  --image gcr.io/your-project-id/sumopod-frontend \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

### Claw Cloud (run.claw.cloud)

1. **Prepare for Claw Cloud**:
```bash
# Build production images
./scripts/build.sh --env=prod --tag=latest

# Push to Docker Hub (Claw Cloud supports Docker Hub)
./scripts/push-to-dockerhub.sh --username=yourusername
```

2. **Deploy to Claw Cloud**:
   - Visit https://run.claw.cloud/
   - Create a new project
   - Add your Docker Hub images:
     - Backend: `yourusername/sumopod-backend:latest`
     - Frontend: `yourusername/sumopod-frontend:latest`
   - Configure environment variables from your `.env.production`
   - Set up database (PostgreSQL)
   - Deploy services

### AWS ECS

1. **Push to Amazon ECR**:
```bash
# Create ECR repositories
aws ecr create-repository --repository-name sumopod-backend
aws ecr create-repository --repository-name sumopod-frontend

# Get login token
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin your-account.dkr.ecr.us-east-1.amazonaws.com

# Tag and push
docker tag sumopod-backend:latest your-account.dkr.ecr.us-east-1.amazonaws.com/sumopod-backend:latest
docker push your-account.dkr.ecr.us-east-1.amazonaws.com/sumopod-backend:latest
```

2. **Create ECS task definition and service**:
   - Use AWS Console or CLI to create ECS cluster
   - Define task definitions with your ECR images
   - Configure environment variables and networking
   - Create services and load balancers

### DigitalOcean App Platform

1. **Connect repository**:
   - Go to DigitalOcean App Platform
   - Connect your GitHub repository
   - Configure build settings to use Dockerfiles

2. **Configure services**:
   - Backend: Use `hono-backend/Dockerfile`
   - Frontend: Use `sumopod/Dockerfile`
   - Database: Add managed PostgreSQL database
   - Set environment variables

## 🔍 Troubleshooting

### Common Issues

#### 1. Database Connection Failed
```bash
# Check if PostgreSQL is running
docker-compose ps postgres

# Check database logs
docker-compose logs postgres

# Verify connection string
echo $DATABASE_URL
```

#### 2. Frontend Can't Connect to Backend
```bash
# Check backend health
curl http://localhost:8080/api/auth/session

# Verify CORS settings
docker-compose logs backend | grep CORS

# Check environment variables
docker-compose exec frontend env | grep VITE_API_BASE_URL
```

#### 3. Build Failures
```bash
# Clear Docker cache
docker system prune -a

# Rebuild without cache
./scripts/build.sh --no-cache

# Check Dockerfile syntax
docker build --dry-run -f hono-backend/Dockerfile hono-backend/
```

#### 4. Permission Issues
```bash
# Fix script permissions
chmod +x scripts/*.sh

# Check Docker permissions
docker run hello-world
```

### Debugging Commands

```bash
# Enter container shell
docker-compose exec backend bash
docker-compose exec frontend sh

# Check container resources
docker stats

# View detailed logs
docker-compose logs --tail=100 -f backend

# Inspect container
docker inspect sumopod-backend
```

## 📚 Best Practices

### Security
- ✅ Use non-root users in containers
- ✅ Implement health checks
- ✅ Use multi-stage builds
- ✅ Scan images for vulnerabilities
- ✅ Use secrets management for production

### Performance
- ✅ Optimize Docker layer caching
- ✅ Use .dockerignore files
- ✅ Minimize image sizes
- ✅ Use Alpine Linux base images
- ✅ Enable gzip compression

### Monitoring
- ✅ Implement comprehensive logging
- ✅ Set up health checks
- ✅ Monitor resource usage
- ✅ Use structured logging
- ✅ Set up alerts for failures

### Development
- ✅ Use Docker Compose for local development
- ✅ Implement hot reload for development
- ✅ Use environment-specific configurations
- ✅ Version your Docker images
- ✅ Document all environment variables

## 📞 Support

If you encounter issues:

1. Check the [Troubleshooting](#troubleshooting) section
2. Review Docker and application logs
3. Verify environment configuration
4. Check network connectivity between services
5. Ensure all required ports are available

For additional help, please refer to the main project documentation or create an issue in the repository.
