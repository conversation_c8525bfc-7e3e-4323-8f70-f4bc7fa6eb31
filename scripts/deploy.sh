#!/bin/bash

# =============================================================================
# Sumopod Docker Deployment Script
# =============================================================================
# This script deploys the Sumopod application using Docker Compose
# Usage: ./scripts/deploy.sh [OPTIONS]
# Options:
#   --env=<environment>    Environment (dev|prod) [default: dev]
#   --action=<action>      Action (up|down|restart|logs|status) [default: up]
#   --build                Build images before deploying
#   --pull                 Pull latest images before deploying
#   --help                 Show this help message

set -euo pipefail

# Default values
ENVIRONMENT="dev"
ACTION="up"
BUILD=false
PULL=false
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
Sumopod Docker Deployment Script

USAGE:
    ./scripts/deploy.sh [OPTIONS]

OPTIONS:
    --env=<environment>    Environment (dev|prod) [default: dev]
    --action=<action>      Action (up|down|restart|logs|status) [default: up]
    --build                Build images before deploying
    --pull                 Pull latest images before deploying
    --help                 Show this help message

ACTIONS:
    up                     Start all services
    down                   Stop and remove all services
    restart                Restart all services
    logs                   Show logs from all services
    status                 Show status of all services

EXAMPLES:
    # Start development environment
    ./scripts/deploy.sh --env=dev

    # Start production environment with build
    ./scripts/deploy.sh --env=prod --build

    # Stop all services
    ./scripts/deploy.sh --action=down

    # Restart services and show logs
    ./scripts/deploy.sh --action=restart && ./scripts/deploy.sh --action=logs

    # Check service status
    ./scripts/deploy.sh --action=status

EOF
}

# Parse command line arguments
for arg in "$@"; do
    case $arg in
        --env=*)
            ENVIRONMENT="${arg#*=}"
            shift
            ;;
        --action=*)
            ACTION="${arg#*=}"
            shift
            ;;
        --build)
            BUILD=true
            shift
            ;;
        --pull)
            PULL=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $arg"
            show_help
            exit 1
            ;;
    esac
done

# Validate environment
if [[ "$ENVIRONMENT" != "dev" && "$ENVIRONMENT" != "prod" ]]; then
    log_error "Invalid environment: $ENVIRONMENT. Must be 'dev' or 'prod'"
    exit 1
fi

# Validate action
if [[ ! "$ACTION" =~ ^(up|down|restart|logs|status)$ ]]; then
    log_error "Invalid action: $ACTION. Must be one of: up, down, restart, logs, status"
    exit 1
fi

# Set compose file based on environment
if [[ "$ENVIRONMENT" == "prod" ]]; then
    COMPOSE_FILE="docker-compose.prod.yml"
else
    COMPOSE_FILE="docker-compose.yml"
fi

log_info "Starting deployment process..."
log_info "Environment: $ENVIRONMENT"
log_info "Action: $ACTION"
log_info "Compose file: $COMPOSE_FILE"

# Change to project root
cd "$PROJECT_ROOT"

# Check if compose file exists
if [[ ! -f "$COMPOSE_FILE" ]]; then
    log_error "Compose file not found: $COMPOSE_FILE"
    exit 1
fi

# Load environment variables
ENV_FILE=".env.${ENVIRONMENT}"
if [[ -f "$ENV_FILE" ]]; then
    log_info "Loading environment variables from $ENV_FILE"
    export $(grep -v '^#' "$ENV_FILE" | xargs)
elif [[ -f ".env" ]]; then
    log_info "Loading environment variables from .env"
    export $(grep -v '^#' ".env" | xargs)
else
    log_warning "No environment file found. Using default values."
fi

# Build images if requested
if [[ "$BUILD" == true ]]; then
    log_info "Building images..."
    if ! "$SCRIPT_DIR/build.sh" --env="$ENVIRONMENT"; then
        log_error "Failed to build images"
        exit 1
    fi
fi

# Pull images if requested
if [[ "$PULL" == true ]]; then
    log_info "Pulling latest images..."
    if ! docker-compose -f "$COMPOSE_FILE" pull; then
        log_error "Failed to pull images"
        exit 1
    fi
fi

# Execute the requested action
case $ACTION in
    up)
        log_info "Starting services..."
        if docker-compose -f "$COMPOSE_FILE" up -d; then
            log_success "Services started successfully"
            echo
            log_info "Service status:"
            docker-compose -f "$COMPOSE_FILE" ps
            echo
            log_info "Access URLs:"
            if [[ "$ENVIRONMENT" == "prod" ]]; then
                echo "  Frontend: http://localhost:${FRONTEND_PORT:-80}"
                echo "  Backend:  http://localhost:${BACKEND_PORT:-8080}"
            else
                echo "  Frontend: http://localhost:3000"
                echo "  Backend:  http://localhost:8080"
                echo "  Frontend (dev): http://localhost:3001"
            fi
        else
            log_error "Failed to start services"
            exit 1
        fi
        ;;
    down)
        log_info "Stopping services..."
        if docker-compose -f "$COMPOSE_FILE" down; then
            log_success "Services stopped successfully"
        else
            log_error "Failed to stop services"
            exit 1
        fi
        ;;
    restart)
        log_info "Restarting services..."
        if docker-compose -f "$COMPOSE_FILE" restart; then
            log_success "Services restarted successfully"
            echo
            log_info "Service status:"
            docker-compose -f "$COMPOSE_FILE" ps
        else
            log_error "Failed to restart services"
            exit 1
        fi
        ;;
    logs)
        log_info "Showing service logs..."
        docker-compose -f "$COMPOSE_FILE" logs -f
        ;;
    status)
        log_info "Service status:"
        docker-compose -f "$COMPOSE_FILE" ps
        echo
        log_info "Service health:"
        docker-compose -f "$COMPOSE_FILE" exec backend curl -f http://localhost:8080/api/auth/session || echo "Backend health check failed"
        docker-compose -f "$COMPOSE_FILE" exec frontend wget --spider -q http://localhost:80/health || echo "Frontend health check failed"
        ;;
esac

log_success "Deployment action completed! 🚀"
