#!/bin/bash

# =============================================================================
# Sumopod Docker Hub Push Script
# =============================================================================
# This script builds and pushes Docker images to Docker Hub
# Usage: ./scripts/push-to-dockerhub.sh [OPTIONS]
# Options:
#   --username=<username>  Docker Hub username [required]
#   --tag=<tag>           Docker image tag [default: latest]
#   --env=<environment>    Environment (dev|prod) [default: prod]
#   --login               Login to Docker Hub before pushing
#   --help                Show this help message

set -euo pipefail

# Default values
USERNAME=""
TAG="latest"
ENVIRONMENT="prod"
LOGIN=false
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
Sumopod Docker Hub Push Script

USAGE:
    ./scripts/push-to-dockerhub.sh --username=<username> [OPTIONS]

OPTIONS:
    --username=<username>  Docker Hub username [required]
    --tag=<tag>           Docker image tag [default: latest]
    --env=<environment>    Environment (dev|prod) [default: prod]
    --login               Login to Docker Hub before pushing
    --help                Show this help message

EXAMPLES:
    # Push production images to Docker Hub
    ./scripts/push-to-dockerhub.sh --username=yourusername

    # Push with custom tag and login
    ./scripts/push-to-dockerhub.sh --username=yourusername --tag=v1.0.0 --login

    # Push development images
    ./scripts/push-to-dockerhub.sh --username=yourusername --env=dev --tag=dev

PREREQUISITES:
    1. Docker must be installed and running
    2. You must have a Docker Hub account
    3. You must have push permissions to the repositories

NOTES:
    - This script will create repositories: yourusername/sumopod-backend and yourusername/sumopod-frontend
    - Make sure to set your Docker Hub username correctly
    - Use --login if you haven't logged in to Docker Hub yet

EOF
}

# Parse command line arguments
for arg in "$@"; do
    case $arg in
        --username=*)
            USERNAME="${arg#*=}"
            shift
            ;;
        --tag=*)
            TAG="${arg#*=}"
            shift
            ;;
        --env=*)
            ENVIRONMENT="${arg#*=}"
            shift
            ;;
        --login)
            LOGIN=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $arg"
            show_help
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$USERNAME" ]]; then
    log_error "Docker Hub username is required. Use --username=<username>"
    show_help
    exit 1
fi

# Validate environment
if [[ "$ENVIRONMENT" != "dev" && "$ENVIRONMENT" != "prod" ]]; then
    log_error "Invalid environment: $ENVIRONMENT. Must be 'dev' or 'prod'"
    exit 1
fi

# Set image names
REGISTRY="${USERNAME}/"
BACKEND_IMAGE="${REGISTRY}sumopod-backend:${TAG}"
FRONTEND_IMAGE="${REGISTRY}sumopod-frontend:${TAG}"

log_info "Starting Docker Hub push process..."
log_info "Username: $USERNAME"
log_info "Tag: $TAG"
log_info "Environment: $ENVIRONMENT"
echo

# Change to project root
cd "$PROJECT_ROOT"

# Login to Docker Hub if requested
if [[ "$LOGIN" == true ]]; then
    log_info "Logging in to Docker Hub..."
    if docker login; then
        log_success "Successfully logged in to Docker Hub"
    else
        log_error "Failed to login to Docker Hub"
        exit 1
    fi
    echo
fi

# Check if user is logged in
if ! docker info | grep -q "Username:"; then
    log_warning "You don't appear to be logged in to Docker Hub"
    log_info "Run with --login flag or execute 'docker login' manually"
    echo
fi

# Build images
log_info "Building images for Docker Hub..."
if ! "$SCRIPT_DIR/build.sh" --env="$ENVIRONMENT" --tag="$TAG" --registry="$REGISTRY"; then
    log_error "Failed to build images"
    exit 1
fi

echo

# Push backend image
log_info "Pushing backend image: $BACKEND_IMAGE"
if docker push "$BACKEND_IMAGE"; then
    log_success "Backend image pushed successfully"
else
    log_error "Failed to push backend image"
    log_error "Make sure you have push permissions to $USERNAME/sumopod-backend"
    exit 1
fi

echo

# Push frontend image
log_info "Pushing frontend image: $FRONTEND_IMAGE"
if docker push "$FRONTEND_IMAGE"; then
    log_success "Frontend image pushed successfully"
else
    log_error "Failed to push frontend image"
    log_error "Make sure you have push permissions to $USERNAME/sumopod-frontend"
    exit 1
fi

echo

# Show final information
log_success "All images pushed successfully to Docker Hub! 🚀"
echo
log_info "Pushed images:"
echo "  Backend:  $BACKEND_IMAGE"
echo "  Frontend: $FRONTEND_IMAGE"
echo
log_info "Docker Hub URLs:"
echo "  Backend:  https://hub.docker.com/r/$USERNAME/sumopod-backend"
echo "  Frontend: https://hub.docker.com/r/$USERNAME/sumopod-frontend"
echo
log_info "To use these images in production, update your .env.production file:"
echo "  DOCKER_REGISTRY=${REGISTRY}"
echo "  TAG=${TAG}"
echo
log_info "Then deploy with:"
echo "  ./scripts/deploy.sh --env=prod --pull"
