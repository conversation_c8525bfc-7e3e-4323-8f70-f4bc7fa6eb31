#!/bin/bash

# =============================================================================
# Sumopod Claw Cloud Deployment Script
# =============================================================================
# This script prepares and deploys Sumopod to Claw Cloud platform
# Usage: ./scripts/deploy-to-claw-cloud.sh [OPTIONS]

set -euo pipefail

# Default values
DOCKER_USERNAME=""
TAG="latest"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
Sumopod Claw Cloud Deployment Script

USAGE:
    ./scripts/deploy-to-claw-cloud.sh --username=<docker-username> [OPTIONS]

OPTIONS:
    --username=<username>  Docker Hub username [required]
    --tag=<tag>           Docker image tag [default: latest]
    --help                Show this help message

EXAMPLES:
    # Deploy to Claw Cloud
    ./scripts/deploy-to-claw-cloud.sh --username=yourusername

    # Deploy with custom tag
    ./scripts/deploy-to-claw-cloud.sh --username=yourusername --tag=v1.0.0

PREREQUISITES:
    1. Docker must be installed and running
    2. You must have a Docker Hub account
    3. You must be logged in to Docker Hub (docker login)
    4. You must have a Claw Cloud account
    5. Production environment must be configured (.env.production)

EOF
}

# Parse command line arguments
for arg in "$@"; do
    case $arg in
        --username=*)
            DOCKER_USERNAME="${arg#*=}"
            shift
            ;;
        --tag=*)
            TAG="${arg#*=}"
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $arg"
            show_help
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$DOCKER_USERNAME" ]]; then
    log_error "Docker Hub username is required. Use --username=<username>"
    show_help
    exit 1
fi

# Set image names
REGISTRY="${DOCKER_USERNAME}/"
BACKEND_IMAGE="${REGISTRY}sumopod-backend:${TAG}"
FRONTEND_IMAGE="${REGISTRY}sumopod-frontend:${TAG}"

log_info "Starting Claw Cloud deployment preparation..."
log_info "Docker Username: $DOCKER_USERNAME"
log_info "Tag: $TAG"
echo

# Change to project root
cd "$PROJECT_ROOT"

# Check if production environment exists
if [[ ! -f ".env.production" ]]; then
    log_error "Production environment file not found!"
    log_error "Please copy .env.production.template to .env.production and configure it."
    exit 1
fi

# Load production environment variables
log_info "Loading production environment variables..."
set -a
source ".env.production"
set +a

# Validate required environment variables
required_vars=(
    "POSTGRES_PASSWORD"
    "BETTER_AUTH_SECRET"
    "BETTER_AUTH_URL"
    "BETTER_AUTH_TRUSTED_ORIGINS"
    "XENDIT_API_KEY"
    "XENDIT_CALLBACK_TOKEN"
    "VITE_API_BASE_URL"
)

log_info "Validating environment variables..."
for var in "${required_vars[@]}"; do
    if [[ -z "${!var:-}" ]] || [[ "${!var}" == *"CHANGE_THIS"* ]]; then
        log_error "Environment variable $var is not properly configured!"
        log_error "Please update .env.production with actual production values."
        exit 1
    fi
done

# Check Docker login
log_info "Checking Docker Hub authentication..."
if ! docker info | grep -q "Username:"; then
    log_error "You are not logged in to Docker Hub!"
    log_error "Please run 'docker login' first."
    exit 1
fi

# Build and push images
log_info "Building and pushing production images..."
if ! "$SCRIPT_DIR/build-production.sh" --registry="$REGISTRY" --tag="$TAG" --push; then
    log_error "Failed to build and push images"
    exit 1
fi

echo
log_success "Images successfully pushed to Docker Hub!"
echo
log_info "Pushed images:"
echo "  Backend:  $BACKEND_IMAGE"
echo "  Frontend: $FRONTEND_IMAGE"
echo

# Generate Claw Cloud deployment configuration
log_info "Generating Claw Cloud deployment configuration..."

cat > claw-cloud-deployment.yml << EOF
# Claw Cloud Deployment Configuration for Sumopod
# Generated on $(date)

services:
  # PostgreSQL Database
  database:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-sumopod}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    resources:
      memory: 512MB
      cpu: 0.5

  # Backend Service
  backend:
    image: ${BACKEND_IMAGE}
    environment:
      DATABASE_URL: postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD}@database:5432/${POSTGRES_DB:-sumopod}
      BETTER_AUTH_SECRET: ${BETTER_AUTH_SECRET}
      BETTER_AUTH_URL: ${BETTER_AUTH_URL}
      BETTER_AUTH_TRUSTED_ORIGINS: ${BETTER_AUTH_TRUSTED_ORIGINS}
      PORT: 8080
      NODE_ENV: production
      CORS_ORIGINS: ${CORS_ORIGINS}
      CORS_ALLOW_HEADERS: Content-Type,Authorization,X-Session-Token
      CORS_ALLOW_METHODS: GET,POST,PUT,DELETE,OPTIONS
      APP_NAME: sumopod-backend
      EXTERNAL_ID_PREFIX: sumopod-
      XENDIT_API_KEY: ${XENDIT_API_KEY}
      XENDIT_API_URL: https://api.xendit.co/v2/invoices
      XENDIT_CALLBACK_TOKEN: ${XENDIT_CALLBACK_TOKEN}
    depends_on:
      - database
    resources:
      memory: 512MB
      cpu: 0.5

  # Frontend Service
  frontend:
    image: ${FRONTEND_IMAGE}
    depends_on:
      - backend
    resources:
      memory: 128MB
      cpu: 0.25

volumes:
  postgres_data:

EOF

log_success "Claw Cloud deployment configuration generated!"
echo

# Display deployment instructions
cat << EOF
${GREEN}=============================================================================
CLAW CLOUD DEPLOYMENT INSTRUCTIONS
=============================================================================${NC}

${BLUE}1. Login to Claw Cloud:${NC}
   Visit: https://run.claw.cloud/
   Login with your account credentials

${BLUE}2. Create a new project:${NC}
   - Click "New Project"
   - Name: sumopod-production
   - Select your preferred region

${BLUE}3. Deploy the services:${NC}
   
   ${YELLOW}Option A: Using the generated configuration file${NC}
   - Upload the generated file: claw-cloud-deployment.yml
   - Claw Cloud will automatically parse and deploy all services
   
   ${YELLOW}Option B: Manual service creation${NC}
   
   a) Create Database Service:
      - Service Type: PostgreSQL
      - Version: 15
      - Memory: 512MB
      - Storage: 10GB
      - Environment Variables:
        * POSTGRES_DB: ${POSTGRES_DB:-sumopod}
        * POSTGRES_USER: ${POSTGRES_USER:-postgres}
        * POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
   
   b) Create Backend Service:
      - Service Type: Container
      - Image: ${BACKEND_IMAGE}
      - Memory: 512MB
      - CPU: 0.5 cores
      - Port: 8080
      - Environment Variables: (copy from .env.production)
   
   c) Create Frontend Service:
      - Service Type: Container
      - Image: ${FRONTEND_IMAGE}
      - Memory: 128MB
      - CPU: 0.25 cores
      - Port: 80

${BLUE}4. Configure networking:${NC}
   - Ensure backend can connect to database
   - Ensure frontend can connect to backend
   - Configure external access for frontend

${BLUE}5. Set up custom domains (optional):${NC}
   - Configure your domain to point to the frontend service
   - Update BETTER_AUTH_URL and CORS_ORIGINS with your actual domain

${BLUE}6. Monitor deployment:${NC}
   - Check service logs for any errors
   - Verify database migrations run successfully
   - Test the application functionality

${GREEN}Your Docker images are ready for deployment!${NC}

${YELLOW}Next steps:${NC}
1. Go to https://run.claw.cloud/
2. Follow the instructions above
3. Monitor your deployment in the Claw Cloud dashboard

${BLUE}Support:${NC}
- Claw Cloud Documentation: https://docs.claw.cloud/
- Docker Hub Images: https://hub.docker.com/u/${DOCKER_USERNAME}

EOF

log_success "Claw Cloud deployment preparation completed! 🚀"
