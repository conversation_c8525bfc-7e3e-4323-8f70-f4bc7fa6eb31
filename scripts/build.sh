#!/bin/bash

# =============================================================================
# Sumopod Docker Build Script
# =============================================================================
# This script builds Docker images for the Sumopod application
# Usage: ./scripts/build.sh [OPTIONS]
# Options:
#   --env=<environment>    Environment (dev|prod) [default: dev]
#   --tag=<tag>           Docker image tag [default: latest]
#   --registry=<registry> Docker registry prefix [default: none]
#   --push                Push images to registry after building
#   --no-cache            Build without using cache
#   --help                Show this help message

set -euo pipefail

# Default values
ENVIRONMENT="dev"
TAG="latest"
REGISTRY=""
PUSH=false
NO_CACHE=""
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
Sumopod Docker Build Script

USAGE:
    ./scripts/build.sh [OPTIONS]

OPTIONS:
    --env=<environment>    Environment (dev|prod) [default: dev]
    --tag=<tag>           Docker image tag [default: latest]
    --registry=<registry> Docker registry prefix [default: none]
    --push                Push images to registry after building
    --no-cache            Build without using cache
    --help                Show this help message

EXAMPLES:
    # Build development images
    ./scripts/build.sh --env=dev

    # Build production images with custom tag
    ./scripts/build.sh --env=prod --tag=v1.0.0

    # Build and push to Docker Hub
    ./scripts/build.sh --env=prod --registry=yourusername/ --push

    # Build without cache
    ./scripts/build.sh --no-cache

EOF
}

# Parse command line arguments
for arg in "$@"; do
    case $arg in
        --env=*)
            ENVIRONMENT="${arg#*=}"
            shift
            ;;
        --tag=*)
            TAG="${arg#*=}"
            shift
            ;;
        --registry=*)
            REGISTRY="${arg#*=}"
            shift
            ;;
        --push)
            PUSH=true
            shift
            ;;
        --no-cache)
            NO_CACHE="--no-cache"
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $arg"
            show_help
            exit 1
            ;;
    esac
done

# Validate environment
if [[ "$ENVIRONMENT" != "dev" && "$ENVIRONMENT" != "prod" ]]; then
    log_error "Invalid environment: $ENVIRONMENT. Must be 'dev' or 'prod'"
    exit 1
fi

# Set image names
BACKEND_IMAGE="${REGISTRY}sumopod-backend:${TAG}"
FRONTEND_IMAGE="${REGISTRY}sumopod-frontend:${TAG}"

log_info "Starting Docker build process..."
log_info "Environment: $ENVIRONMENT"
log_info "Tag: $TAG"
log_info "Registry: ${REGISTRY:-none}"
log_info "Push: $PUSH"

# Change to project root
cd "$PROJECT_ROOT"

# Load environment variables
if [[ -f ".env.${ENVIRONMENT}" ]]; then
    log_info "Loading environment variables from .env.${ENVIRONMENT}"
    set -a
    source ".env.${ENVIRONMENT}"
    set +a
elif [[ -f ".env" ]]; then
    log_info "Loading environment variables from .env"
    set -a
    source ".env"
    set +a
else
    log_warning "No environment file found. Using default values."
fi

# Build backend image
log_info "Building backend image: $BACKEND_IMAGE"
if docker build $NO_CACHE -t "$BACKEND_IMAGE" -f hono-backend/Dockerfile hono-backend/; then
    log_success "Backend image built successfully"
else
    log_error "Failed to build backend image"
    exit 1
fi

# Build frontend image
log_info "Building frontend image: $FRONTEND_IMAGE"
FRONTEND_BUILD_ARGS=""
if [[ -n "${VITE_API_BASE_URL:-}" ]]; then
    FRONTEND_BUILD_ARGS="$FRONTEND_BUILD_ARGS --build-arg VITE_API_BASE_URL=$VITE_API_BASE_URL"
fi
if [[ -n "${VITE_GOLD_API_URL:-}" ]]; then
    FRONTEND_BUILD_ARGS="$FRONTEND_BUILD_ARGS --build-arg VITE_GOLD_API_URL=$VITE_GOLD_API_URL"
fi

if docker build $NO_CACHE $FRONTEND_BUILD_ARGS -t "$FRONTEND_IMAGE" -f sumopod/Dockerfile sumopod/; then
    log_success "Frontend image built successfully"
else
    log_error "Failed to build frontend image"
    exit 1
fi

# Push images if requested
if [[ "$PUSH" == true ]]; then
    if [[ -z "$REGISTRY" ]]; then
        log_error "Cannot push images without registry prefix. Use --registry option."
        exit 1
    fi

    log_info "Pushing images to registry..."
    
    if docker push "$BACKEND_IMAGE"; then
        log_success "Backend image pushed successfully"
    else
        log_error "Failed to push backend image"
        exit 1
    fi

    if docker push "$FRONTEND_IMAGE"; then
        log_success "Frontend image pushed successfully"
    else
        log_error "Failed to push frontend image"
        exit 1
    fi
fi

# Show image information
log_info "Build completed successfully!"
echo
log_info "Built images:"
echo "  Backend:  $BACKEND_IMAGE"
echo "  Frontend: $FRONTEND_IMAGE"
echo
log_info "Image sizes:"
docker images --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}" | grep -E "(sumopod-backend|sumopod-frontend)" | head -3

log_success "All done! 🚀"
