#!/bin/bash

# =============================================================================
# Sumopod Production Deployment Script
# =============================================================================
# This script deploys the Sumopod application using production Docker Compose
# Usage: ./scripts/deploy-production.sh [OPTIONS]

set -euo pipefail

# Default values
ACTION="up"
BUILD=false
PULL=false
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
Sumopod Production Deployment Script

USAGE:
    ./scripts/deploy-production.sh [OPTIONS]

OPTIONS:
    --action=<action>      Action (up|down|restart|logs|status) [default: up]
    --build                Build images before deploying
    --pull                 Pull latest images before deploying
    --help                 Show this help message

ACTIONS:
    up                     Start all services
    down                   Stop and remove all services
    restart                Restart all services
    logs                   Show logs from all services
    status                 Show status of all services

EXAMPLES:
    # Start production environment
    ./scripts/deploy-production.sh

    # Start with build
    ./scripts/deploy-production.sh --build

    # Stop all services
    ./scripts/deploy-production.sh --action=down

    # Check service status
    ./scripts/deploy-production.sh --action=status

EOF
}

# Parse command line arguments
for arg in "$@"; do
    case $arg in
        --action=*)
            ACTION="${arg#*=}"
            shift
            ;;
        --build)
            BUILD=true
            shift
            ;;
        --pull)
            PULL=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $arg"
            show_help
            exit 1
            ;;
    esac
done

# Validate action
if [[ ! "$ACTION" =~ ^(up|down|restart|logs|status)$ ]]; then
    log_error "Invalid action: $ACTION. Must be one of: up, down, restart, logs, status"
    exit 1
fi

COMPOSE_FILE="docker-compose.production.yml"

log_info "Starting production deployment..."
log_info "Action: $ACTION"
log_info "Compose file: $COMPOSE_FILE"

# Change to project root
cd "$PROJECT_ROOT"

# Check if compose file exists
if [[ ! -f "$COMPOSE_FILE" ]]; then
    log_error "Production compose file not found: $COMPOSE_FILE"
    exit 1
fi

# Load production environment variables
if [[ -f ".env.production" ]]; then
    log_info "Loading production environment variables"
    export $(grep -v '^#' ".env.production" | xargs)
else
    log_error "Production environment file not found!"
    log_error "Please copy .env.production.template to .env.production and configure it."
    exit 1
fi

# Build images if requested
if [[ "$BUILD" == true ]]; then
    log_info "Building production images..."
    if ! "$SCRIPT_DIR/build-production.sh"; then
        log_error "Failed to build images"
        exit 1
    fi
fi

# Pull images if requested
if [[ "$PULL" == true ]]; then
    log_info "Pulling latest images..."
    if ! docker-compose -f "$COMPOSE_FILE" pull; then
        log_error "Failed to pull images"
        exit 1
    fi
fi

# Execute the requested action
case $ACTION in
    up)
        log_info "Starting production services..."
        if docker-compose -f "$COMPOSE_FILE" up -d; then
            log_success "Production services started successfully"
            echo
            log_info "Service status:"
            docker-compose -f "$COMPOSE_FILE" ps
            echo
            log_info "Application URLs:"
            echo "  Frontend: Available on the configured domain"
            echo "  Backend:  Available on the configured domain"
            echo
            log_warning "Note: Configure your reverse proxy/load balancer to route traffic to these containers"
        else
            log_error "Failed to start services"
            exit 1
        fi
        ;;
    down)
        log_info "Stopping production services..."
        if docker-compose -f "$COMPOSE_FILE" down; then
            log_success "Production services stopped successfully"
        else
            log_error "Failed to stop services"
            exit 1
        fi
        ;;
    restart)
        log_info "Restarting production services..."
        if docker-compose -f "$COMPOSE_FILE" restart; then
            log_success "Production services restarted successfully"
            echo
            log_info "Service status:"
            docker-compose -f "$COMPOSE_FILE" ps
        else
            log_error "Failed to restart services"
            exit 1
        fi
        ;;
    logs)
        log_info "Showing production service logs..."
        docker-compose -f "$COMPOSE_FILE" logs -f
        ;;
    status)
        log_info "Production service status:"
        docker-compose -f "$COMPOSE_FILE" ps
        echo
        log_info "Container resource usage:"
        docker stats --no-stream $(docker-compose -f "$COMPOSE_FILE" ps -q) 2>/dev/null || echo "No running containers"
        ;;
esac

log_success "Production deployment action completed! 🚀"
