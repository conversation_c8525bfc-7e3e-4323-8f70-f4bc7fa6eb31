#!/bin/bash

# =============================================================================
# Sumopod Production Build Script
# =============================================================================
# This script builds production-ready Docker images for cloud deployment
# Usage: ./scripts/build-production.sh [OPTIONS]

set -euo pipefail

# Default values
TAG="latest"
REGISTRY=""
PUSH=false
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
Sumopod Production Build Script

USAGE:
    ./scripts/build-production.sh [OPTIONS]

OPTIONS:
    --tag=<tag>           Docker image tag [default: latest]
    --registry=<registry> Docker registry prefix [default: none]
    --push                Push images to registry after building
    --help                Show this help message

EXAMPLES:
    # Build production images
    ./scripts/build-production.sh

    # Build with custom tag
    ./scripts/build-production.sh --tag=v1.0.0

    # Build and push to Docker Hub
    ./scripts/build-production.sh --registry=yourusername/ --push

EOF
}

# Parse command line arguments
for arg in "$@"; do
    case $arg in
        --tag=*)
            TAG="${arg#*=}"
            shift
            ;;
        --registry=*)
            REGISTRY="${arg#*=}"
            shift
            ;;
        --push)
            PUSH=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $arg"
            show_help
            exit 1
            ;;
    esac
done

# Set image names
BACKEND_IMAGE="${REGISTRY}sumopod-backend:${TAG}"
FRONTEND_IMAGE="${REGISTRY}sumopod-frontend:${TAG}"

log_info "Starting production build process..."
log_info "Tag: $TAG"
log_info "Registry: ${REGISTRY:-none}"
log_info "Push: $PUSH"

# Change to project root
cd "$PROJECT_ROOT"

# Load production environment variables
if [[ -f ".env.production" ]]; then
    log_info "Loading production environment variables"
    set -a
    source ".env.production"
    set +a
else
    log_error "Production environment file not found!"
    log_error "Please copy .env.production.template to .env.production and configure it."
    exit 1
fi

# Validate required environment variables
required_vars=(
    "POSTGRES_PASSWORD"
    "BETTER_AUTH_SECRET"
    "BETTER_AUTH_URL"
    "BETTER_AUTH_TRUSTED_ORIGINS"
    "XENDIT_API_KEY"
    "XENDIT_CALLBACK_TOKEN"
    "VITE_API_BASE_URL"
)

for var in "${required_vars[@]}"; do
    if [[ -z "${!var:-}" ]]; then
        log_error "Required environment variable $var is not set!"
        exit 1
    fi
done

# Build backend image
log_info "Building backend image: $BACKEND_IMAGE"
if docker build --no-cache -t "$BACKEND_IMAGE" -f hono-backend/Dockerfile hono-backend/; then
    log_success "Backend image built successfully"
else
    log_error "Failed to build backend image"
    exit 1
fi

# Build frontend image
log_info "Building frontend image: $FRONTEND_IMAGE"
if docker build --no-cache \
    --build-arg VITE_API_BASE_URL="$VITE_API_BASE_URL" \
    --build-arg VITE_GOLD_API_URL="$VITE_GOLD_API_URL" \
    -t "$FRONTEND_IMAGE" \
    -f sumopod/Dockerfile sumopod/; then
    log_success "Frontend image built successfully"
else
    log_error "Failed to build frontend image"
    exit 1
fi

# Push images if requested
if [[ "$PUSH" == true ]]; then
    if [[ -z "$REGISTRY" ]]; then
        log_error "Cannot push images without registry prefix. Use --registry option."
        exit 1
    fi

    log_info "Pushing images to registry..."
    
    if docker push "$BACKEND_IMAGE"; then
        log_success "Backend image pushed successfully"
    else
        log_error "Failed to push backend image"
        exit 1
    fi

    if docker push "$FRONTEND_IMAGE"; then
        log_success "Frontend image pushed successfully"
    else
        log_error "Failed to push frontend image"
        exit 1
    fi
fi

# Show image information
log_info "Production build completed successfully!"
echo
log_info "Built images:"
echo "  Backend:  $BACKEND_IMAGE"
echo "  Frontend: $FRONTEND_IMAGE"
echo
log_info "Image sizes:"
docker images --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}" | grep -E "(sumopod-backend|sumopod-frontend)" | head -3

log_success "Production images ready for deployment! 🚀"
