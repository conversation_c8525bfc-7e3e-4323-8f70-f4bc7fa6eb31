# 🚀 Sumopod Production Deployment Guide

This guide provides step-by-step instructions for deploying the Sumopod application to production using Docker containers optimized for cloud platforms.

## 📋 Overview

The production setup includes:
- **Streamlined Docker containers** without development dependencies
- **Production-optimized configurations** for performance and security
- **Cloud-ready deployment** for platforms like Claw Cloud
- **Automated build and deployment scripts**

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Database      │
│   (Nginx)       │◄──►│   (Node.js)     │◄──►│  (PostgreSQL)   │
│   Port: 80      │    │   Port: 8080    │    │   Port: 5432    │
│   128MB RAM     │    │   512MB RAM     │    │   512MB RAM     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔧 Prerequisites

1. **Docker Desktop** (version 20.10+) installed and running
2. **Docker Hub account** for image registry
3. **Claw Cloud account** for deployment
4. **Production environment configuration**

## 📝 Step 1: Configure Production Environment

### 1.1 Create Production Environment File

```bash
# Copy the production template
cp .env.production.template .env.production

# Edit with your production values
nano .env.production
```

### 1.2 Required Configuration

Update these critical values in `.env.production`:

```bash
# Database (use a strong password)
POSTGRES_PASSWORD=your-very-secure-production-password

# Authentication (generate a 64+ character secret)
BETTER_AUTH_SECRET=your-production-secret-key-at-least-64-characters-long

# URLs (update with your actual domains)
BETTER_AUTH_URL=https://api.your-domain.com
BETTER_AUTH_TRUSTED_ORIGINS=https://your-domain.com
VITE_API_BASE_URL=https://api.your-domain.com
CORS_ORIGINS=https://your-domain.com

# Xendit (use production credentials)
XENDIT_API_KEY=your-production-xendit-api-key
XENDIT_CALLBACK_TOKEN=your-production-xendit-callback-token
```

## 🔨 Step 2: Build Production Images

### 2.1 Login to Docker Hub

```bash
docker login
```

### 2.2 Build and Push Images

```bash
# Build production images and push to Docker Hub
./scripts/build-production.sh --registry=yourusername/ --push

# Or build locally first, then push
./scripts/build-production.sh --tag=v1.0.0
./scripts/push-to-dockerhub.sh --username=yourusername --tag=v1.0.0
```

### 2.3 Verify Images in Docker Desktop

1. Open Docker Desktop
2. Go to **Images** tab
3. Verify you see:
   - `yourusername/sumopod-backend:latest`
   - `yourusername/sumopod-frontend:latest`

## 🌐 Step 3: Deploy to Claw Cloud

### 3.1 Prepare for Claw Cloud Deployment

```bash
# This script builds, pushes, and generates deployment config
./scripts/deploy-to-claw-cloud.sh --username=yourusername
```

### 3.2 Deploy on Claw Cloud Platform

1. **Login to Claw Cloud**:
   - Visit: https://run.claw.cloud/
   - Login with your account

2. **Create New Project**:
   - Click "New Project"
   - Name: `sumopod-production`
   - Select your preferred region

3. **Deploy Services**:

   **Option A: Upload Configuration File**
   - Upload the generated `claw-cloud-deployment.yml`
   - Claw Cloud will automatically deploy all services

   **Option B: Manual Service Creation**

   a) **Create Database Service**:
   ```
   Service Type: PostgreSQL
   Version: 15
   Memory: 512MB
   Storage: 10GB
   Environment Variables:
     POSTGRES_DB: sumopod
     POSTGRES_USER: postgres
     POSTGRES_PASSWORD: [your-secure-password]
   ```

   b) **Create Backend Service**:
   ```
   Service Type: Container
   Image: yourusername/sumopod-backend:latest
   Memory: 512MB
   CPU: 0.5 cores
   Port: 8080
   Environment Variables: [copy from .env.production]
   ```

   c) **Create Frontend Service**:
   ```
   Service Type: Container
   Image: yourusername/sumopod-frontend:latest
   Memory: 128MB
   CPU: 0.25 cores
   Port: 80
   ```

4. **Configure Networking**:
   - Ensure services can communicate internally
   - Configure external access for frontend
   - Set up load balancer if needed

## 🔍 Step 4: Verify Deployment

### 4.1 Check Service Status

In Claw Cloud dashboard:
- Verify all services are running
- Check resource usage
- Monitor logs for errors

### 4.2 Test Application

1. **Frontend Access**: Visit your frontend URL
2. **Backend API**: Test API endpoints
3. **Database**: Verify data persistence
4. **Authentication**: Test login/logout
5. **Payments**: Test Xendit integration

## 📊 Step 5: Monitor and Maintain

### 5.1 Docker Desktop Monitoring

1. Open Docker Desktop
2. Go to **Containers** tab
3. Monitor running containers:
   - Resource usage
   - Logs
   - Performance metrics

### 5.2 Production Monitoring

- **Logs**: Monitor application logs in Claw Cloud
- **Performance**: Track response times and errors
- **Resources**: Monitor CPU and memory usage
- **Database**: Monitor database performance

## 🔄 Step 6: Updates and Maintenance

### 6.1 Deploy Updates

```bash
# Build new version
./scripts/build-production.sh --registry=yourusername/ --tag=v1.1.0 --push

# Update services in Claw Cloud with new image tags
```

### 6.2 Backup Database

```bash
# Create database backup (run on Claw Cloud)
pg_dump -h database -U postgres sumopod > backup_$(date +%Y%m%d).sql
```

## 🛠️ Troubleshooting

### Common Issues

1. **Build Failures**:
   ```bash
   # Clear Docker cache
   docker system prune -a
   
   # Rebuild without cache
   ./scripts/build-production.sh --registry=yourusername/ --push
   ```

2. **Environment Variable Issues**:
   ```bash
   # Verify all required variables are set
   grep -v '^#' .env.production | grep 'CHANGE_THIS'
   ```

3. **Service Communication**:
   - Check network configuration in Claw Cloud
   - Verify service names and ports
   - Check firewall rules

4. **Database Connection**:
   - Verify DATABASE_URL format
   - Check database service status
   - Verify credentials

## 📚 Additional Resources

- **Claw Cloud Documentation**: https://docs.claw.cloud/
- **Docker Hub**: https://hub.docker.com/
- **Production Environment Template**: `.env.production.template`
- **Deployment Configuration**: `claw-cloud-deployment.yml`

## 🔐 Security Best Practices

1. **Never commit** `.env.production` to version control
2. **Use strong passwords** for all services
3. **Regularly rotate** API keys and secrets
4. **Enable SSL/TLS** for all endpoints
5. **Monitor** for security vulnerabilities
6. **Keep images updated** with security patches

## 📞 Support

For deployment issues:
1. Check Claw Cloud documentation
2. Review application logs
3. Verify environment configuration
4. Test locally with production compose file

---

**🎉 Congratulations!** Your Sumopod application is now deployed to production and ready to serve users.
