-- Initialize Sumopod Database
-- This script runs when the PostgreSQL container starts for the first time

-- Create database if it doesn't exist (handled by POSTGRES_DB env var)
-- The database 'sumopod' is automatically created by the postgres image

-- Set timezone
SET timezone = 'UTC';

-- Create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE sumopod TO postgres;

-- Log initialization
DO $$
BEGIN
    RAISE NOTICE 'Sumopod database initialized successfully at %', NOW();
END $$;
