version: '3.8'

# Production Docker Compose for Sumopod Application
# Optimized for cloud deployment without local development dependencies

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: sumopod-postgres
    restart: always
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-sumopod}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - sumopod-network
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Backend Service
  backend:
    build:
      context: ./hono-backend
      dockerfile: Dockerfile
      target: production
    container_name: sumopod-backend
    restart: always
    environment:
      # Database
      DATABASE_URL: postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-sumopod}
      
      # Authentication
      BETTER_AUTH_SECRET: ${BETTER_AUTH_SECRET}
      BETTER_AUTH_URL: ${BETTER_AUTH_URL}
      BETTER_AUTH_TRUSTED_ORIGINS: ${BETTER_AUTH_TRUSTED_ORIGINS}
      
      # Server
      PORT: 8080
      NODE_ENV: production
      
      # CORS
      CORS_ORIGINS: ${CORS_ORIGINS}
      CORS_ALLOW_HEADERS: ${CORS_ALLOW_HEADERS:-Content-Type,Authorization,X-Session-Token}
      CORS_ALLOW_METHODS: ${CORS_ALLOW_METHODS:-GET,POST,PUT,DELETE,OPTIONS}
      
      # Application
      APP_NAME: ${APP_NAME:-sumopod-backend}
      EXTERNAL_ID_PREFIX: ${EXTERNAL_ID_PREFIX:-sumopod-}
      
      # Xendit Payment
      XENDIT_API_KEY: ${XENDIT_API_KEY}
      XENDIT_API_URL: ${XENDIT_API_URL:-https://api.xendit.co/v2/invoices}
      XENDIT_CALLBACK_TOKEN: ${XENDIT_CALLBACK_TOKEN}
    depends_on:
      - postgres
    networks:
      - sumopod-network
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Frontend Service
  frontend:
    build:
      context: ./sumopod
      dockerfile: Dockerfile
      target: production
      args:
        VITE_API_BASE_URL: ${VITE_API_BASE_URL}
        VITE_GOLD_API_URL: ${VITE_GOLD_API_URL:-https://logam-mulia-api.vercel.app/prices/anekalogam}
    container_name: sumopod-frontend
    restart: always
    depends_on:
      - backend
    networks:
      - sumopod-network
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  sumopod-network:
    driver: bridge
    name: sumopod-network

volumes:
  postgres_data:
    name: sumopod-postgres-data
