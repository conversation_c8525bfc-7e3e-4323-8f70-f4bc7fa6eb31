# =============================================================================
# DEVELOPMENT ENVIRONMENT CONFIGURATION
# =============================================================================
# This file contains safe default values for development
# Copy to .env and modify as needed

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
POSTGRES_DB=sumopod
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_PORT=5432
DATABASE_URL=********************************************/sumopod

# =============================================================================
# BACKEND AUTHENTICATION CONFIGURATION
# =============================================================================
BETTER_AUTH_SECRET=development-secret-key-change-in-production
BETTER_AUTH_URL=http://localhost:8080
BETTER_AUTH_TRUSTED_ORIGINS=http://localhost:3000,http://localhost:3001

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
BACKEND_PORT=8080
FRONTEND_PORT=3000
FRONTEND_SSL_PORT=443

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
CORS_ORIGINS=http://localhost:3000,http://localhost:3001
CORS_ALLOW_HEADERS=Content-Type,Authorization,X-Session-Token
CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
APP_NAME=sumopod-backend
EXTERNAL_ID_PREFIX=sumopod-dev-

# =============================================================================
# XENDIT PAYMENT CONFIGURATION (DEVELOPMENT)
# =============================================================================
# Use Xendit test/sandbox credentials for development
XENDIT_API_KEY=your-xendit-test-api-key
XENDIT_API_URL=https://api.xendit.co/v2/invoices
XENDIT_CALLBACK_TOKEN=your-xendit-test-callback-token

# =============================================================================
# FRONTEND ENVIRONMENT VARIABLES
# =============================================================================
VITE_API_BASE_URL=http://localhost:8080
VITE_GOLD_API_URL=https://logam-mulia-api.vercel.app/prices/anekalogam

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================
TAG=dev
DOCKER_REGISTRY=
