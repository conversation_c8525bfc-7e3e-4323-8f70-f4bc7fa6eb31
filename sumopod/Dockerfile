# syntax = docker/dockerfile:1

# Production-ready frontend Dockerfile for Sumopod
ARG NODE_VERSION=20.18.0
ARG NGINX_VERSION=1.25-alpine

# Base stage with Node.js
FROM node:${NODE_VERSION}-slim as base

WORKDIR /app

# Install system dependencies
RUN apt-get update -qq && \
    apt-get install -y --no-install-recommends \
    ca-certificates \
    && apt-get upgrade -y \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user
RUN groupadd --gid 1001 --system nodejs && \
    useradd --uid 1001 --system --gid nodejs --create-home --shell /bin/bash nodejs

# Set environment variables
ENV NPM_CONFIG_UPDATE_NOTIFIER=false
ENV NPM_CONFIG_FUND=false

# Dependencies stage
FROM base as deps

COPY --chown=nodejs:nodejs package*.json ./
RUN npm ci --frozen-lockfile && \
    npm cache clean --force

# Build stage
FROM base as build

# Copy dependencies and source code
COPY --from=deps --chown=nodejs:nodejs /app/node_modules ./node_modules
COPY --chown=nodejs:nodejs package*.json ./
COPY --chown=nodejs:nodejs tsconfig*.json ./
COPY --chown=nodejs:nodejs vite.config.ts ./
COPY --chown=nodejs:nodejs index.html ./
COPY --chown=nodejs:nodejs postcss.config.cjs ./
COPY --chown=nodejs:nodejs tailwind.config.cjs ./
COPY --chown=nodejs:nodejs biome.json ./
COPY --chown=nodejs:nodejs public ./public
COPY --chown=nodejs:nodejs src ./src

# Build arguments for environment variables
ARG VITE_API_BASE_URL
ARG VITE_GOLD_API_URL

# Set build environment variables
ENV VITE_API_BASE_URL=${VITE_API_BASE_URL}
ENV VITE_GOLD_API_URL=${VITE_GOLD_API_URL}

# Build the application
RUN npm run build

# Production stage with Nginx
FROM nginx:${NGINX_VERSION} as production

# Install security updates
RUN apk update && \
    apk upgrade && \
    apk add --no-cache ca-certificates && \
    rm -rf /var/cache/apk/*

# Copy built application and nginx configuration
COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

# Create necessary directories and set permissions
RUN mkdir -p /var/cache/nginx /var/log/nginx /var/run && \
    chown -R nginx:nginx /var/cache/nginx /var/log/nginx /var/run /usr/share/nginx/html && \
    chmod -R 755 /var/cache/nginx /var/log/nginx /usr/share/nginx/html

# Switch to non-root user
USER nginx

# Expose port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
