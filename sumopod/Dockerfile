# syntax = docker/dockerfile:1

# Build arguments
ARG NODE_VERSION=20.18.0
ARG NGINX_VERSION=1.25-alpine

# Base stage with Node.js
FROM node:${NODE_VERSION}-slim as base

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update -qq && \
    apt-get install -y --no-install-recommends \
    ca-certificates \
    && apt-get upgrade -y \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user
RUN groupadd --gid 1001 --system nodejs && \
    useradd --uid 1001 --system --gid nodejs --create-home --shell /bin/bash nodejs

# Set environment variables
ENV NPM_CONFIG_UPDATE_NOTIFIER=false
ENV NPM_CONFIG_FUND=false

# Dependencies stage
FROM base as deps

# Copy package files
COPY --chown=nodejs:nodejs package*.json ./

# Install dependencies
RUN npm ci --frozen-lockfile && \
    npm cache clean --force

# Build stage
FROM base as build

# Copy dependencies from deps stage
COPY --from=deps --chown=nodejs:nodejs /app/node_modules ./node_modules

# Copy source code
COPY --chown=nodejs:nodejs package*.json ./
COPY --chown=nodejs:nodejs tsconfig*.json ./
COPY --chown=nodejs:nodejs vite.config.ts ./
COPY --chown=nodejs:nodejs index.html ./
COPY --chown=nodejs:nodejs postcss.config.cjs ./
COPY --chown=nodejs:nodejs tailwind.config.cjs ./
COPY --chown=nodejs:nodejs biome.json ./
COPY --chown=nodejs:nodejs public ./public
COPY --chown=nodejs:nodejs src ./src

# Build arguments for environment variables
ARG VITE_API_BASE_URL
ARG VITE_GOLD_API_URL

# Set build environment variables
ENV VITE_API_BASE_URL=${VITE_API_BASE_URL}
ENV VITE_GOLD_API_URL=${VITE_GOLD_API_URL}

# Build the application
RUN npm run build

# Production stage with Nginx
FROM nginx:${NGINX_VERSION} as production

# Install security updates
RUN apk update && \
    apk upgrade && \
    apk add --no-cache \
    ca-certificates \
    && rm -rf /var/cache/apk/*

# Create nginx user and group if they don't exist
RUN addgroup -g 101 -S nginx || true && \
    adduser -S -D -H -u 101 -h /var/cache/nginx -s /sbin/nologin -G nginx -g nginx nginx || true

# Copy built application
COPY --from=build --chown=nginx:nginx /app/dist /usr/share/nginx/html

# Copy custom nginx configuration
COPY --chown=nginx:nginx nginx.conf /etc/nginx/nginx.conf

# Create nginx directories and set permissions
RUN mkdir -p /var/cache/nginx/client_temp \
    /var/cache/nginx/proxy_temp \
    /var/cache/nginx/fastcgi_temp \
    /var/cache/nginx/uwsgi_temp \
    /var/cache/nginx/scgi_temp \
    /var/log/nginx \
    /var/run \
    && chown -R nginx:nginx /var/cache/nginx \
    /var/log/nginx \
    /var/run \
    /usr/share/nginx/html \
    && chmod -R 755 /var/cache/nginx \
    /var/log/nginx \
    /usr/share/nginx/html

# Switch to non-root user
USER nginx

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:80/ || exit 1

# Expose port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
