# =============================================================================
# Sumopod Cloud Deployment Configuration
# =============================================================================
# This file contains configuration templates for various cloud platforms

# =============================================================================
# Google Cloud Run Configuration
# =============================================================================
gcp:
  project_id: "your-project-id"
  region: "us-central1"
  
  backend:
    service_name: "sumopod-backend"
    image: "gcr.io/your-project-id/sumopod-backend:latest"
    port: 8080
    memory: "512Mi"
    cpu: "1"
    min_instances: 0
    max_instances: 10
    environment_variables:
      - name: "NODE_ENV"
        value: "production"
      - name: "PORT"
        value: "8080"
      # Add other environment variables from .env.production
    
  frontend:
    service_name: "sumopod-frontend"
    image: "gcr.io/your-project-id/sumopod-frontend:latest"
    port: 80
    memory: "256Mi"
    cpu: "0.5"
    min_instances: 0
    max_instances: 5

# =============================================================================
# AWS ECS Configuration
# =============================================================================
aws:
  region: "us-east-1"
  cluster_name: "sumopod-cluster"
  
  backend:
    task_definition: "sumopod-backend-task"
    service_name: "sumopod-backend-service"
    image: "your-account.dkr.ecr.us-east-1.amazonaws.com/sumopod-backend:latest"
    cpu: 512
    memory: 1024
    port: 8080
    desired_count: 2
    
  frontend:
    task_definition: "sumopod-frontend-task"
    service_name: "sumopod-frontend-service"
    image: "your-account.dkr.ecr.us-east-1.amazonaws.com/sumopod-frontend:latest"
    cpu: 256
    memory: 512
    port: 80
    desired_count: 2

# =============================================================================
# Claw Cloud Configuration
# =============================================================================
claw_cloud:
  project_name: "sumopod"
  
  backend:
    name: "sumopod-backend"
    image: "yourusername/sumopod-backend:latest"
    port: 8080
    instances: 1
    memory: "512MB"
    cpu: "0.5"
    
  frontend:
    name: "sumopod-frontend"
    image: "yourusername/sumopod-frontend:latest"
    port: 80
    instances: 1
    memory: "256MB"
    cpu: "0.25"
    
  database:
    type: "postgresql"
    version: "15"
    storage: "10GB"
    memory: "1GB"

# =============================================================================
# DigitalOcean App Platform Configuration
# =============================================================================
digitalocean:
  name: "sumopod-app"
  region: "nyc"
  
  services:
    - name: "backend"
      source_dir: "/hono-backend"
      dockerfile_path: "hono-backend/Dockerfile"
      instance_count: 1
      instance_size_slug: "basic-xxs"
      http_port: 8080
      
    - name: "frontend"
      source_dir: "/sumopod"
      dockerfile_path: "sumopod/Dockerfile"
      instance_count: 1
      instance_size_slug: "basic-xxs"
      http_port: 80
      
  databases:
    - name: "sumopod-db"
      engine: "PG"
      version: "15"
      size: "db-s-1vcpu-1gb"

# =============================================================================
# Azure Container Instances Configuration
# =============================================================================
azure:
  resource_group: "sumopod-rg"
  location: "eastus"
  
  backend:
    name: "sumopod-backend"
    image: "yourusername/sumopod-backend:latest"
    cpu: 1
    memory: 1
    port: 8080
    
  frontend:
    name: "sumopod-frontend"
    image: "yourusername/sumopod-frontend:latest"
    cpu: 0.5
    memory: 0.5
    port: 80

# =============================================================================
# Kubernetes Configuration Template
# =============================================================================
kubernetes:
  namespace: "sumopod"
  
  backend:
    deployment:
      name: "sumopod-backend"
      replicas: 2
      image: "yourusername/sumopod-backend:latest"
      port: 8080
      resources:
        requests:
          memory: "256Mi"
          cpu: "250m"
        limits:
          memory: "512Mi"
          cpu: "500m"
    
    service:
      name: "sumopod-backend-service"
      type: "ClusterIP"
      port: 8080
      
  frontend:
    deployment:
      name: "sumopod-frontend"
      replicas: 2
      image: "yourusername/sumopod-frontend:latest"
      port: 80
      resources:
        requests:
          memory: "128Mi"
          cpu: "100m"
        limits:
          memory: "256Mi"
          cpu: "200m"
    
    service:
      name: "sumopod-frontend-service"
      type: "LoadBalancer"
      port: 80

# =============================================================================
# Environment Variables Template
# =============================================================================
environment_variables:
  required:
    - DATABASE_URL
    - BETTER_AUTH_SECRET
    - BETTER_AUTH_URL
    - BETTER_AUTH_TRUSTED_ORIGINS
    - XENDIT_API_KEY
    - XENDIT_CALLBACK_TOKEN
    - VITE_API_BASE_URL
    
  optional:
    - CORS_ORIGINS
    - CORS_ALLOW_HEADERS
    - CORS_ALLOW_METHODS
    - APP_NAME
    - EXTERNAL_ID_PREFIX
    - VITE_GOLD_API_URL

# =============================================================================
# Health Check Endpoints
# =============================================================================
health_checks:
  backend:
    path: "/api/auth/session"
    port: 8080
    interval: 30
    timeout: 10
    retries: 3
    
  frontend:
    path: "/health"
    port: 80
    interval: 30
    timeout: 5
    retries: 3

# =============================================================================
# Scaling Configuration
# =============================================================================
scaling:
  backend:
    min_instances: 1
    max_instances: 10
    cpu_threshold: 70
    memory_threshold: 80
    
  frontend:
    min_instances: 1
    max_instances: 5
    cpu_threshold: 60
    memory_threshold: 70
