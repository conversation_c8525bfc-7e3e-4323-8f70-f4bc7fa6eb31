# =============================================================================
# SUMOPOD APPLICATION ENVIRONMENT CONFIGURATION
# =============================================================================
# Copy this file to .env and fill in your actual values
# Never commit .env files to version control!

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
POSTGRES_DB=sumopod
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your-secure-postgres-password
POSTGRES_PORT=5432

# Full database URL (used by backend)
DATABASE_URL=*****************************************************************/sumopod

# =============================================================================
# BACKEND AUTHENTICATION CONFIGURATION
# =============================================================================
# Generate a secure random string for production (at least 32 characters)
BETTER_AUTH_SECRET=your-super-secret-key-change-this-in-production-min-32-chars

# Your backend URL (change for production)
BETTER_AUTH_URL=http://localhost:8080

# Comma-separated list of trusted frontend URLs
BETTER_AUTH_TRUSTED_ORIGINS=http://localhost:3000,http://localhost:3001,https://your-frontend-domain.com

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
# Backend port
BACKEND_PORT=8080

# Frontend port
FRONTEND_PORT=3000

# Frontend SSL port (for production)
FRONTEND_SSL_PORT=443

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# Comma-separated list of allowed origins
CORS_ORIGINS=http://localhost:3000,http://localhost:3001,https://your-frontend-domain.com

# Comma-separated list of allowed headers
CORS_ALLOW_HEADERS=Content-Type,Authorization,X-Session-Token

# Comma-separated list of allowed HTTP methods
CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
APP_NAME=sumopod-backend
EXTERNAL_ID_PREFIX=sumopod-

# =============================================================================
# XENDIT PAYMENT CONFIGURATION
# =============================================================================
# Get these from your Xendit dashboard
XENDIT_API_KEY=your-xendit-api-key
XENDIT_API_URL=https://api.xendit.co/v2/invoices
XENDIT_CALLBACK_TOKEN=your-xendit-callback-token

# =============================================================================
# FRONTEND ENVIRONMENT VARIABLES
# =============================================================================
# Backend API URL (used by frontend)
VITE_API_BASE_URL=http://localhost:8080

# Gold API URL (external service)
VITE_GOLD_API_URL=https://logam-mulia-api.vercel.app/prices/anekalogam

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================
# Docker image tag
TAG=latest

# Docker registry (leave empty for local builds)
DOCKER_REGISTRY=

# =============================================================================
# PRODUCTION ONLY CONFIGURATION
# =============================================================================
# Set these only in production environments

# Production database password (use a strong password)
# POSTGRES_PASSWORD=your-very-secure-production-password

# Production authentication secret (generate a secure random string)
# BETTER_AUTH_SECRET=your-production-secret-key-at-least-64-characters-long

# Production URLs
# BETTER_AUTH_URL=https://api.your-domain.com
# BETTER_AUTH_TRUSTED_ORIGINS=https://your-domain.com,https://www.your-domain.com
# VITE_API_BASE_URL=https://api.your-domain.com

# Production CORS origins
# CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com

# Production Xendit credentials
# XENDIT_API_KEY=your-production-xendit-api-key
# XENDIT_CALLBACK_TOKEN=your-production-xendit-callback-token
