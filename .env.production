# =============================================================================
# PRODUCTION ENVIRONMENT CONFIGURATION
# =============================================================================
# IMPORTANT: Fill in all values before deploying to production
# Never use default or development values in production!

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
POSTGRES_DB=sumopod
POSTGRES_USER=postgres
# CRITICAL: Use a strong, unique password for production
POSTGRES_PASSWORD=CHANGE_THIS_TO_A_SECURE_PASSWORD
POSTGRES_PORT=5432
DATABASE_URL=********************************************************************/sumopod

# =============================================================================
# BACKEND AUTHENTICATION CONFIGURATION
# =============================================================================
# CRITICAL: Generate a secure random string (at least 64 characters)
BETTER_AUTH_SECRET=CHANGE_THIS_TO_A_SECURE_RANDOM_STRING_AT_LEAST_64_CHARS

# CRITICAL: Update with your actual production URLs
BETTER_AUTH_URL=https://api.your-domain.com
BETTER_AUTH_TRUSTED_ORIGINS=https://your-domain.com,https://www.your-domain.com

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
BACKEND_PORT=8080
FRONTEND_PORT=80
FRONTEND_SSL_PORT=443

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# CRITICAL: Update with your actual production domains
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com
CORS_ALLOW_HEADERS=Content-Type,Authorization,X-Session-Token
CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
APP_NAME=sumopod-backend
EXTERNAL_ID_PREFIX=sumopod-

# =============================================================================
# XENDIT PAYMENT CONFIGURATION (PRODUCTION)
# =============================================================================
# CRITICAL: Use your production Xendit credentials
XENDIT_API_KEY=CHANGE_THIS_TO_YOUR_PRODUCTION_XENDIT_API_KEY
XENDIT_API_URL=https://api.xendit.co/v2/invoices
XENDIT_CALLBACK_TOKEN=CHANGE_THIS_TO_YOUR_PRODUCTION_XENDIT_CALLBACK_TOKEN

# =============================================================================
# FRONTEND ENVIRONMENT VARIABLES
# =============================================================================
# CRITICAL: Update with your actual production API URL
VITE_API_BASE_URL=https://api.your-domain.com
VITE_GOLD_API_URL=https://logam-mulia-api.vercel.app/prices/anekalogam

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================
TAG=latest
# Set this if using a private registry (e.g., your-registry.com/)
DOCKER_REGISTRY=

# =============================================================================
# SECURITY NOTES
# =============================================================================
# 1. Never commit this file to version control
# 2. Use environment-specific secrets management in production
# 3. Regularly rotate passwords and API keys
# 4. Use strong, unique passwords for all services
# 5. Enable SSL/TLS for all production endpoints
# 6. Consider using Docker secrets for sensitive data
